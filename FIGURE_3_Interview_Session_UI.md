# Figure 3: User Interface - Interview Session

## Description
The interview session interface provides an interactive chat environment where users engage with the AI interviewer in real-time, simulating a realistic interview experience.

## Layout Structure

### Header with Progress
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ [Home] AI Job Interview Coach                    Question 7/15  [⚙️Settings]│
│                                                                             │
│ Progress: ████████████████████████████████████████████████░░░░░░░░░░ 47%    │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Main Interview Area
```
┌─────────────────────────────────────────────────────────────────────────────┐
│                           INTERVIEW SESSION                                │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  🤖 AI Interviewer                                               10:23 AM   │
│  ┌─────────────────────────────────────────────────────────────────────┐   │
│  │ Hello! I've reviewed your CV and I'm excited to discuss your       │   │
│  │ experience in software development. Let's start with a question     │   │
│  │ about your recent project at TechCorp.                             │   │
│  │                                                                     │   │
│  │ Can you tell me about a challenging technical problem you          │   │
│  │ encountered during your internship and how you solved it?          │   │
│  └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│  👤 You                                                               10:24 AM│
│  ┌─────────────────────────────────────────────────────────────────────┐   │
│  │ During my internship at TechCorp, I faced a significant challenge  │   │
│  │ with database performance optimization. The application was         │   │
│  │ experiencing slow query times that affected user experience...      │   │
│  └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│  🤖 AI Interviewer                                               10:25 AM   │
│  ┌─────────────────────────────────────────────────────────────────────┐   │
│  │ That sounds like a complex challenge. Can you walk me through       │   │
│  │ the specific steps you took to identify the root cause of the       │   │
│  │ performance issues?                                                 │   │
│  └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│  💭 AI is typing...                                                         │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Response Input Area
```
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│  ┌─────────────────────────────────────────────────────────────────────┐   │
│  │ Type your response here...                                          │   │
│  │                                                                     │   │
│  │ 💡 Tip: Structure your answer using STAR method                    │   │
│  │    (Situation, Task, Action, Result)                               │   │
│  │                                                                     │   │
│  │                                                                     │   │
│  └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│  [📎 Attach File]  [🎤 Voice Input]           [Send] [⏸️ Pause Session]    │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Sidebar (Collapsible)
```
┌─────────────────────┐
│   SESSION INFO      │
├─────────────────────┤
│                     │
│ 📊 Current Stats    │
│ • Questions: 7/15   │
│ • Time: 23:45       │
│ • Avg Response: 2m  │
│                     │
│ 📝 Session Notes    │
│ • Strong technical  │
│   knowledge shown   │
│ • Good use of STAR  │
│   in Q3 and Q5      │
│                     │
│ 🎯 Focus Areas      │
│ • Leadership exp.   │
│ • Team collaboration│
│ • Problem solving   │
│                     │
│ 📋 Quick Actions    │
│ [View Feedback]     │
│ [End Session]       │
│ [Save & Exit]       │
│                     │
│ [◀ Close Sidebar]   │
└─────────────────────┘
```

## Interactive Features

### Message Types
1. **AI Questions**: Blue background, robot icon
2. **User Responses**: Gray background, user icon  
3. **System Messages**: Light background, info icon
4. **Typing Indicators**: Animated dots showing AI is processing

### Real-time Features
- **Live typing indicators** when AI is generating responses
- **Message timestamps** for each exchange
- **Read receipts** showing message delivery status
- **Auto-scroll** to latest messages

### STAR Framework Helper
```
┌─────────────────────────────────────────────────────────────────────────────┐
│                           STAR Framework Guide                             │
├─────────────────────────────────────────────────────────────────────────────┤
│  📍 Situation: Describe the context and background                         │
│  🎯 Task: Explain what needed to be accomplished                           │
│  ⚡ Action: Detail the specific steps you took                             │
│  🏆 Result: Share the outcomes and what you learned                        │
│                                                                             │
│  [Show Example] [Hide Guide]                                               │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Session Controls
- **Pause/Resume**: Temporarily stop the interview
- **Save Progress**: Save current state to continue later
- **End Session**: Complete the interview and view feedback
- **Emergency Exit**: Quick exit with auto-save

## Responsive Design

### Desktop (1200px+)
- Full sidebar visible
- Wide chat area with comfortable message spacing
- Multi-line input field with formatting options

### Tablet (768px - 1199px)
- Collapsible sidebar (hidden by default)
- Optimized chat area width
- Touch-friendly input controls

### Mobile (< 768px)
- No sidebar (accessible via menu)
- Full-width chat interface
- Mobile keyboard optimization
- Swipe gestures for navigation

## Accessibility Features

### Keyboard Navigation
- Tab through all interactive elements
- Enter to send messages
- Escape to access menu
- Arrow keys for message history

### Screen Reader Support
- Proper ARIA labels for all elements
- Live regions for new messages
- Descriptive text for icons and buttons
- Structured heading hierarchy

### Visual Accessibility
- High contrast mode support
- Scalable text (up to 200%)
- Focus indicators on all interactive elements
- Color-blind friendly design

## Technical Implementation

### State Management
- Real-time message synchronization
- Session persistence across page refreshes
- Optimistic UI updates for better UX
- Error handling with retry mechanisms

### Performance Optimization
- Message virtualization for long conversations
- Lazy loading of message history
- Efficient re-rendering with React.memo
- Debounced typing indicators

### Security Features
- Input sanitization for all user messages
- XSS protection for message content
- Secure session token management
- Rate limiting for message sending

## User Experience Flow

1. **Session Start**: Welcome message and context setting
2. **Question Flow**: AI asks personalized questions based on CV
3. **Response Collection**: User types responses with STAR guidance
4. **Follow-up Questions**: AI generates contextual follow-ups
5. **Progress Tracking**: Visual progress through 15-question session
6. **Session Completion**: Automatic transition to feedback page
