# Figure 1: System Architecture Diagram

## Mermaid Diagram Code

```mermaid
graph TB
    subgraph "Presentation Layer"
        A[React Frontend<br/>TypeScript + Material-UI]
        A1[User Interface Components]
        A2[State Management<br/>Context API]
        A3[Routing<br/>React Router]
        A --> A1
        A --> A2
        A --> A3
    end

    subgraph "Application Layer"
        B[Flask Backend<br/>Python REST API]
        B1[Authentication<br/>JWT Tokens]
        B2[Session Management]
        B3[CV Processing<br/>python-docx + NLP]
        B4[Error Handling]
        B --> B1
        B --> B2
        B --> B3
        B --> B4
    end

    subgraph "AI Services Layer"
        C1[Groq API<br/>Gemini 2.0 Flash]
        C2[Ollama<br/>llama3 Local]
        C3[Prompt Engineering]
        C4[Context Management]
        C1 --> C3
        C2 --> C3
        C3 --> C4
    end

    subgraph "Data Layer"
        D1[SQLite Database<br/>User Data & Sessions]
        D2[File Storage<br/>CV Uploads]
        D3[Configuration<br/>Environment Variables]
    end

    subgraph "External Services"
        E1[Groq Cloud API]
        E2[Local Ollama Instance]
    end

    %% Data Flow Connections
    A -.->|HTTP Requests| B
    B -.->|API Calls| C1
    B -.->|Local Calls| C2
    B -.->|Store/Retrieve| D1
    B -.->|File Operations| D2
    B -.->|Config Access| D3
    C1 -.->|Cloud Processing| E1
    C2 -.->|Local Processing| E2

    %% Styling
    classDef frontend fill:#e1f5fe
    classDef backend fill:#f3e5f5
    classDef ai fill:#e8f5e8
    classDef data fill:#fff3e0
    classDef external fill:#ffebee

    class A,A1,A2,A3 frontend
    class B,B1,B2,B3,B4 backend
    class C1,C2,C3,C4 ai
    class D1,D2,D3 data
    class E1,E2 external
```

## Key Architectural Decisions:

1. **Separation of Concerns**: Clear separation between presentation, application, AI services, and data layers
2. **Dual AI Provider**: Primary cloud service with local fallback for reliability and privacy
3. **RESTful API**: Standard HTTP/REST communication between frontend and backend
4. **Component-Based Frontend**: React components for reusability and maintainability
5. **Service-Oriented Backend**: Modular services for different functionalities
6. **Lightweight Database**: SQLite for simplicity, easily upgradeable to PostgreSQL
7. **Adapter Pattern**: Common interface for different AI providers
8. **Stateless Design**: Backend services are stateless for scalability
