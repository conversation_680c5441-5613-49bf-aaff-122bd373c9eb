# Figure 1: System Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                           AI JOB INTERVIEW COACH                           │
│                              SYSTEM ARCHITECTURE                           │
└─────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────┐
│                              PRESENTATION LAYER                            │
├─────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐            │
│  │   Home Page     │  │  User Profile   │  │ Interview Chat  │            │
│  │                 │  │                 │  │                 │            │
│  │ • Welcome       │  │ • Registration  │  │ • Real-time     │            │
│  │ • Features      │  │ • CV Upload     │  │   Conversation  │            │
│  │ • Navigation    │  │ • Profile Mgmt  │  │ • Question Flow │            │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘            │
│                                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐            │
│  │ Feedback View   │  │ Session History │  │ Settings/Theme  │            │
│  │                 │  │                 │  │                 │            │
│  │ • STAR Analysis │  │ • Past Sessions │  │ • Light/Dark    │            │
│  │ • Improvements  │  │ • Progress      │  │ • Preferences   │            │
│  │ • Scoring       │  │ • Resume        │  │ • Account       │            │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘            │
│                                                                             │
│                    React 18 + TypeScript + Material-UI                     │
└─────────────────────────────────────────────────────────────────────────────┘
                                      │
                                      │ HTTP/HTTPS
                                      │ REST API Calls
                                      │
┌─────────────────────────────────────────────────────────────────────────────┐
│                              APPLICATION LAYER                             │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐            │
│  │  Auth Service   │  │   CV Service    │  │  Chat Service   │            │
│  │                 │  │                 │  │                 │            │
│  │ • JWT Tokens    │  │ • DOCX Parsing  │  │ • Message Mgmt  │            │
│  │ • User Sessions │  │ • Text Extract  │  │ • Context Track │            │
│  │ • Validation    │  │ • Skill Extract │  │ • Session State │            │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘            │
│                                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐            │
│  │ Question Gen    │  │ Feedback Gen    │  │ Session Mgmt    │            │
│  │                 │  │                 │  │                 │            │
│  │ • AI Prompts    │  │ • STAR Analysis │  │ • Save/Resume   │            │
│  │ • Personalize   │  │ • Scoring       │  │ • History       │            │
│  │ • Adaptive      │  │ • Suggestions   │  │ • Progress      │            │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘            │
│                                                                             │
│                         Flask + Python + SQLAlchemy                        │
└─────────────────────────────────────────────────────────────────────────────┘
                                      │
                                      │ API Calls
                                      │
┌─────────────────────────────────────────────────────────────────────────────┐
│                               AI SERVICES LAYER                            │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  ┌─────────────────────────────────┐  ┌─────────────────────────────────┐  │
│  │         GROQ API                │  │         OLLAMA LOCAL            │  │
│  │      (Primary Service)          │  │      (Fallback Service)         │  │
│  │                                 │  │                                 │  │
│  │ ┌─────────────────────────────┐ │  │ ┌─────────────────────────────┐ │  │
│  │ │      Gemini 2.0 Flash       │ │  │ │         llama3 8B           │ │  │
│  │ │                             │ │  │ │                             │ │  │
│  │ │ • Question Generation       │ │  │ │ • Question Generation       │ │  │
│  │ │ • Response Analysis         │ │  │ │ • Response Analysis         │ │  │
│  │ │ • STAR Feedback             │ │  │ │ • STAR Feedback             │ │  │
│  │ │ • Context Understanding     │ │  │ │ • Context Understanding     │ │  │
│  │ └─────────────────────────────┘ │  │ └─────────────────────────────┘ │  │
│  │                                 │  │                                 │  │
│  │ • Cloud-based                   │  │ • Local processing              │  │
│  │ • High performance              │  │ • Privacy focused               │  │
│  │ • Latest models                 │  │ • No internet required          │  │
│  └─────────────────────────────────┘  └─────────────────────────────────┘  │
│                                                                             │
│                            AI Adapter Pattern                              │
└─────────────────────────────────────────────────────────────────────────────┘
                                      │
                                      │ Database Queries
                                      │
┌─────────────────────────────────────────────────────────────────────────────┐
│                               DATA LAYER                                   │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐            │
│  │     Users       │  │    Profiles     │  │    Sessions     │            │
│  │                 │  │                 │  │                 │            │
│  │ • ID            │  │ • User ID       │  │ • ID            │            │
│  │ • Username      │  │ • CV Data       │  │ • User ID       │            │
│  │ • Email         │  │ • Skills        │  │ • Start Time    │            │
│  │ • Password      │  │ • Experience    │  │ • End Time      │            │
│  │ • Created At    │  │ • Preferences   │  │ • Type          │            │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘            │
│                                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐            │
│  │    Messages     │  │    Feedback     │  │   User Stats    │            │
│  │                 │  │                 │  │                 │            │
│  │ • ID            │  │ • ID            │  │ • User ID       │            │
│  │ • Session ID    │  │ • Response ID   │  │ • Total Sessions│            │
│  │ • Content       │  │ • STAR Scores   │  │ • Avg Scores    │            │
│  │ • Timestamp     │  │ • Suggestions   │  │ • Last Updated  │            │
│  │ • Type          │  │ • Overall Score │  │ • Progress      │            │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘            │
│                                                                             │
│                              SQLite Database                               │
└─────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────┐
│                              DATA FLOW                                     │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ 1. User uploads CV → CV Service extracts data → Stored in Profile          │
│ 2. User starts interview → Session created → AI generates first question   │
│ 3. User responds → Message saved → AI analyzes response → Feedback gen     │
│ 4. AI generates next question based on CV + previous responses             │
│ 5. Process repeats for 15 questions → Session ends → Summary provided      │
│ 6. User can review session history and track progress over time            │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

## Key Architectural Decisions:

1. **Separation of Concerns**: Clear separation between presentation, application, AI services, and data layers
2. **Dual AI Provider**: Primary cloud service with local fallback for reliability and privacy
3. **RESTful API**: Standard HTTP/REST communication between frontend and backend
4. **Component-Based Frontend**: React components for reusability and maintainability
5. **Service-Oriented Backend**: Modular services for different functionalities
6. **Lightweight Database**: SQLite for simplicity, easily upgradeable to PostgreSQL
7. **Adapter Pattern**: Common interface for different AI providers
8. **Stateless Design**: Backend services are stateless for scalability
