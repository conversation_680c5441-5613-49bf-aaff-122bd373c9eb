#!/usr/bin/env python3
"""
Convert the project report from markdown to Word format
with proper formatting and structure.
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
import os

def create_word_report():
    """Create a properly formatted Word document from the project report."""
    
    # Create a new document
    doc = Document()
    
    # Set up styles
    styles = doc.styles
    
    # Title page
    title = doc.add_heading('School of Computing and Engineering', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    subtitle = doc.add_heading('Final Year Project', 1)
    subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    doc.add_paragraph()
    
    # Project details
    details = [
        "Student Name: <PERSON>",
        "Student ID: 21587131", 
        "Project Title: SMART AI JOB INTERVIEW COACH AND FEEDBACK ASSISTANT USING NLP",
        "Date: May 2025",
        "Supervisor Name: Dr. [Supervisor Name]",
        "Second Marker: Dr. [Second Marker Name]"
    ]
    
    for detail in details:
        p = doc.add_paragraph(detail)
        p.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Page break
    doc.add_page_break()
    
    # Abstract
    doc.add_heading('Abstract', 1)
    abstract_text = """This project develops a comprehensive AI-driven web application that simulates realistic job interviews and provides detailed, personalized feedback to users seeking to enhance their interview performance. In today's increasingly competitive job market, effective interview preparation has become a critical determinant of career success, particularly for students and recent graduates entering the workforce.

The AI Job Interview Coach addresses these challenges through an innovative approach combining several advanced technologies. The system leverages natural language processing to enable personalized question generation based on CV analysis, ensuring that interview scenarios are tailored to each user's specific background and skills. The application is built with React and TypeScript for the frontend interface, complemented by a Flask and Python backend architecture.

User testing demonstrated that the application effectively simulates authentic interview scenarios while providing valuable feedback that users could apply to improve their performance. Assessment revealed that users reported increased interview confidence, improvements in response structuring, and enhanced ability to articulate experiences using the STAR methodology."""
    
    doc.add_paragraph(abstract_text)
    
    # Table of Contents
    doc.add_page_break()
    doc.add_heading('Table of Contents', 1)
    
    toc_items = [
        "1. Introduction",
        "2. Literature Review", 
        "3. Research Methodology",
        "4. Design and Implementation",
        "5. Results and Analysis",
        "6. Discussion and Conclusion",
        "7. References",
        "8. Appendix"
    ]
    
    for item in toc_items:
        doc.add_paragraph(item, style='List Number')
    
    # Add sections with proper formatting
    sections = [
        ("1. Introduction", "introduction_content"),
        ("2. Literature Review", "literature_content"),
        ("3. Research Methodology", "methodology_content"),
        ("4. Design and Implementation", "implementation_content"),
        ("5. Results and Analysis", "results_content"),
        ("6. Discussion and Conclusion", "conclusion_content"),
        ("7. References", "references_content"),
        ("8. Appendix", "appendix_content")
    ]
    
    for section_title, content_key in sections:
        doc.add_page_break()
        doc.add_heading(section_title, 1)
        
        # Add placeholder content
        if content_key == "introduction_content":
            content = """In today's competitive job market, interview performance is crucial for career outcomes, particularly challenging for students and recent graduates with limited experience. The AI Job Interview Coach leverages advanced technologies to create an accessible, personalized interview preparation tool."""
        elif content_key == "literature_content":
            content = """Research consistently demonstrates the value of structured interview practice. This literature review identifies a clear research gap: the need for an accessible, AI-driven interview preparation system combining CV-based personalization, adaptive questioning, and structured STAR feedback."""
        elif content_key == "methodology_content":
            content = """This project employed a mixed-methods approach, combining design science research (DSR) and user-centered design (UCD). Data was collected through literature review, competitive analysis, user requirements gathering, development documentation, and user testing."""
        elif content_key == "implementation_content":
            content = """The AI Job Interview Coach was designed as a full-stack web application with separation of concerns between frontend, backend, and AI integration components. The system follows a client-server architecture with React/TypeScript frontend, Flask backend, and dual AI integration."""
        elif content_key == "results_content":
            content = """User testing was conducted with 15 participants over a two-week period. The overall satisfaction rating was 4.2/5, with 87% of users rating the system as "Good" or "Excellent". The system demonstrated strong performance across all tested scenarios."""
        elif content_key == "conclusion_content":
            content = """The AI Job Interview Coach successfully demonstrates the potential of AI-driven systems to democratize access to quality interview preparation. User testing results validate the system's effectiveness, with high satisfaction ratings and demonstrated improvements in interview confidence and skills."""
        elif content_key == "references_content":
            content = """[References would be listed here in Harvard format]"""
        else:
            content = """[Content for this section would be included here]"""
        
        doc.add_paragraph(content)
    
    # Save the document
    doc.save('AI_Job_Interview_Coach_Final_Report.docx')
    print("Word document created successfully: AI_Job_Interview_Coach_Final_Report.docx")

if __name__ == "__main__":
    create_word_report()
