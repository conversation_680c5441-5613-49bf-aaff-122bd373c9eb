# Figure 5: CV Analysis Process Flow

## Mermaid Diagram Code

```mermaid
flowchart TD
    A[User Uploads CV Document<br/>.docx file] --> B[File Validation<br/>• Check format<br/>• Check size<br/>• Security scan]
    B --> C{Valid File?}
    C -->|No| D[Return Error<br/>• Invalid format<br/>• File too large<br/>• Security issue]
    C -->|Yes| E[Document Parsing<br/>• Extract text<br/>• Preserve structure<br/>• Handle tables]
    
    E --> F[Text Preprocessing<br/>• Clean text<br/>• Remove noise<br/>• Normalize formatting]
    
    F --> G[Section Identification<br/>• Personal Info<br/>• Education<br/>• Experience<br/>• Skills<br/>• Projects]
    
    G --> H[Information Extraction]
    
    H --> H1[Personal Info<br/>• Name<br/>• Email<br/>• Phone<br/>• Location<br/>• LinkedIn]
    H --> H2[Education<br/>• Degree<br/>• Institution<br/>• Graduation<br/>• GPA/Grade<br/>• Coursework]
    H --> H3[Experience<br/>• Job Titles<br/>• Companies<br/>• Duration<br/>• Achievements<br/>• Technologies]
    H --> H4[Skills<br/>• Technical<br/>• Programming<br/>• Languages<br/>• Soft Skills<br/>• Tools]
    H --> H5[Projects<br/>• Project Names<br/>• Technologies<br/>• Description<br/>• Duration<br/>• Team Size]
    H --> H6[Certifications<br/>• Cert Names<br/>• Issuing Org<br/>• Issue Date<br/>• Expiry Date]
    
    H1 --> I[Data Processing]
    H2 --> I
    H3 --> I
    H4 --> I
    H5 --> I
    H6 --> I
    
    I --> I1[Skill Mapping<br/>• Categorize skills<br/>• Proficiency levels<br/>• Skill gaps<br/>• Relevance scoring]
    I --> I2[Experience Analysis<br/>• Calculate total exp<br/>• Career progression<br/>• Leadership experience]
    I --> I3[Industry Classification<br/>• Identify target industries<br/>• Match job categories<br/>• Suggest roles]
    
    I1 --> J[Data Validation<br/>• Check completeness<br/>• Verify consistency<br/>• Flag issues]
    I2 --> J
    I3 --> J
    
    J --> K[Profile Generation<br/>• Create user profile<br/>• Set preferences<br/>• Generate summary<br/>• Calculate scores]
    
    K --> L[Database Storage<br/>• Save profile<br/>• Link to user<br/>• Update timestamp]
    
    L --> M[Return Response<br/>• Success status<br/>• Profile data<br/>• Extracted information<br/>• Analysis results]
    
    D --> N[End Process]
    M --> N
    
    %% Styling
    classDef startEnd fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
    classDef process fill:#e1f5fe,stroke:#2196f3,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    classDef error fill:#ffebee,stroke:#f44336,stroke-width:2px
    classDef success fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px
    classDef extraction fill:#e8f5e8,stroke:#4caf50,stroke-width:1px
    classDef processing fill:#fff8e1,stroke:#ffc107,stroke-width:1px
    
    class A,N startEnd
    class B,E,F,G,H,J,K,L process
    class C decision
    class D error
    class M success
    class H1,H2,H3,H4,H5,H6 extraction
    class I1,I2,I3 processing
```

## Process Description

### 1. File Upload & Validation
- User uploads CV document in .docx format
- System validates file format, size, and security
- Invalid files are rejected with appropriate error messages

### 2. Document Processing
- Parse DOCX structure using python-docx library
- Extract raw text while preserving formatting
- Clean and normalize text data

### 3. Section Identification
- Use pattern matching to identify CV sections
- Recognize standard headings and structures
- Handle variations in CV formatting

### 4. Information Extraction
- Extract structured data from each section
- Use NLP techniques for entity recognition
- Validate and categorize extracted information

### 5. Data Processing & Analysis
- Map skills to standard categories
- Calculate experience levels and progression
- Classify industry and role preferences

### 6. Profile Generation
- Create comprehensive user profile
- Generate summary and insights
- Store in database for future use

### Error Handling
- Comprehensive validation at each step
- Graceful handling of malformed documents
- Clear error messages for user feedback

### Output
- Structured JSON profile data
- Extracted information summary
- Analysis results and recommendations
