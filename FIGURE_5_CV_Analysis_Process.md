# Figure 5: CV Analysis Process Flow

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                           CV ANALYSIS PROCESS FLOW                         │
└─────────────────────────────────────────────────────────────────────────────┘

┌─────────────────┐
│   User Uploads  │
│   CV File       │
│   (.docx)       │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│  File Validation│
│                 │
│ • Check format  │
│ • Check size    │
│ • Virus scan    │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│  Document       │
│  Parsing        │
│                 │
│ • Extract text  │
│ • Preserve      │
│   structure     │
│ • Handle tables │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│  Text           │
│  Preprocessing  │
│                 │
│ • Clean text    │
│ • Remove noise  │
│ • Normalize     │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│  Section        │
│  Identification │
│                 │
│ • Personal Info │
│ • Education     │
│ • Experience    │
│ • Skills        │
│ • Projects      │
└─────────┬───────┘
          │
          ▼
┌─────────────────────────────────────────────────────────────────────────────┐
│                        INFORMATION EXTRACTION                              │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐            │
│  │ Personal Info   │  │   Education     │  │   Experience    │            │
│  │                 │  │                 │  │                 │            │
│  │ • Name          │  │ • Degree        │  │ • Job Titles    │            │
│  │ • Email         │  │ • Institution   │  │ • Companies     │            │
│  │ • Phone         │  │ • Graduation    │  │ • Duration      │            │
│  │ • Location      │  │ • GPA/Grade     │  │ • Achievements  │            │
│  │ • LinkedIn      │  │ • Relevant      │  │ • Technologies  │            │
│  └─────────────────┘  │   Coursework    │  │ • Responsibilities│           │
│                       └─────────────────┘  └─────────────────┘            │
│                                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐            │
│  │     Skills      │  │    Projects     │  │  Certifications │            │
│  │                 │  │                 │  │                 │            │
│  │ • Technical     │  │ • Project Names │  │ • Cert Names    │            │
│  │ • Programming   │  │ • Technologies  │  │ • Issuing Org   │            │
│  │ • Languages     │  │ • Description   │  │ • Issue Date    │            │
│  │ • Soft Skills   │  │ • Duration      │  │ • Expiry Date   │            │
│  │ • Tools         │  │ • Team Size     │  │ • Verification  │            │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘            │
└─────────────────────────────────────────────────────────────────────────────┘
          │
          ▼
┌─────────────────────────────────────────────────────────────────────────────┐
│                           DATA PROCESSING                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐            │
│  │ Skill Mapping   │  │ Experience      │  │ Industry        │            │
│  │                 │  │ Analysis        │  │ Classification  │            │
│  │ • Categorize    │  │                 │  │                 │            │
│  │   skills        │  │ • Calculate     │  │ • Identify      │            │
│  │ • Proficiency   │  │   total exp     │  │   target        │            │
│  │   levels        │  │ • Identify      │  │   industries    │            │
│  │ • Skill gaps    │  │   career        │  │ • Match job     │            │
│  │ • Relevance     │  │   progression   │  │   categories    │            │
│  │   scoring       │  │ • Leadership    │  │ • Suggest       │            │
│  └─────────────────┘  │   experience    │  │   roles         │            │
│                       └─────────────────┘  └─────────────────┘            │
└─────────────────────────────────────────────────────────────────────────────┘
          │
          ▼
┌─────────────────┐
│  Data           │
│  Validation     │
│                 │
│ • Check         │
│   completeness  │
│ • Verify        │
│   consistency   │
│ • Flag issues   │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│  Profile        │
│  Generation     │
│                 │
│ • Create user   │
│   profile       │
│ • Set           │
│   preferences   │
│ • Generate      │
│   summary       │
└─────────┬───────┘
          │
          ▼
┌─────────────────────────────────────────────────────────────────────────────┐
│                        QUESTION PERSONALIZATION                            │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐            │
│  │ Experience-     │  │ Skill-Based     │  │ Industry-       │            │
│  │ Based Questions │  │ Questions       │  │ Specific        │            │
│  │                 │  │                 │  │ Questions       │            │
│  │ • "Tell me      │  │ • "How would    │  │ • Role-specific │            │
│  │   about your    │  │   you use       │  │   scenarios     │            │
│  │   experience    │  │   Python in...?"│  │ • Industry      │            │
│  │   at [Company]" │  │ • "Describe     │  │   challenges    │            │
│  │ • "What was     │  │   your          │  │ • Domain        │            │
│  │   challenging   │  │   experience    │  │   knowledge     │            │
│  │   about [Role]?"│  │   with [Tech]?" │  │   questions     │            │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘            │
└─────────────────────────────────────────────────────────────────────────────┘
          │
          ▼
┌─────────────────┐
│  Store Profile  │
│  Data           │
│                 │
│ • Save to       │
│   database      │
│ • Create        │
│   backup        │
│ • Set flags     │
│   for updates   │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│  Success        │
│  Response       │
│                 │
│ • Confirm       │
│   upload        │
│ • Show          │
│   extracted     │
│   summary       │
│ • Ready for     │
│   interview     │
└─────────────────┘

┌─────────────────────────────────────────────────────────────────────────────┐
│                              ERROR HANDLING                                │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐            │
│  │ File Format     │  │ Parsing Errors  │  │ Extraction      │            │
│  │ Errors          │  │                 │  │ Failures        │            │
│  │                 │  │ • Corrupted     │  │                 │            │
│  │ • Unsupported   │  │   document      │  │ • Missing       │            │
│  │   format        │  │ • Password      │  │   sections      │            │
│  │ • File too      │  │   protected     │  │ • Unclear       │            │
│  │   large         │  │ • Encoding      │  │   formatting    │            │
│  │ • Empty file    │  │   issues        │  │ • Incomplete    │            │
│  └─────────────────┘  └─────────────────┘  │   data          │            │
│                                            └─────────────────┘            │
│                                                                             │
│  Fallback Strategy:                                                         │
│  • Manual data entry option                                                │
│  • Guided profile creation                                                 │
│  • Template-based input                                                    │
│  • Progressive enhancement                                                  │
└─────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────┐
│                           TECHNICAL IMPLEMENTATION                         │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ Libraries Used:                                                             │
│ • python-docx: Document parsing                                            │
│ • spaCy: Named entity recognition                                          │
│ • regex: Pattern matching                                                  │
│ • nltk: Text processing                                                    │
│                                                                             │
│ Processing Time: ~2-5 seconds for typical CV                               │
│ Success Rate: ~95% for standard formats                                    │
│ Supported Formats: .docx (primary), .pdf (future)                         │
└─────────────────────────────────────────────────────────────────────────────┘
```

## Key Features of CV Analysis:

1. **Robust Parsing**: Handles various CV formats and structures
2. **Intelligent Extraction**: Uses NLP to identify and categorize information
3. **Data Validation**: Ensures extracted data quality and completeness
4. **Personalization**: Creates tailored question sets based on CV content
5. **Error Handling**: Graceful fallbacks for parsing failures
6. **Privacy**: Local processing, no data sent to external services
7. **Performance**: Fast processing with caching for repeated operations
