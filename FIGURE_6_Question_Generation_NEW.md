# Figure 6: Question Generation Algorithm

## Mermaid Diagram Code

```mermaid
flowchart TD
    A[Session Start<br/>• Load user profile<br/>• Initialize context<br/>• Set session parameters] --> B[Context Analysis<br/>• CV data analysis<br/>• Experience level<br/>• Previous responses<br/>• Question history]
    
    B --> C[Question Type Selection<br/>• Behavioral<br/>• Technical<br/>• Situational<br/>• Experience-based<br/>• Skills-focused]
    
    C --> D[Difficulty Assessment<br/>• User experience level<br/>• Previous performance<br/>• Adaptive scaling<br/>• Progressive difficulty]
    
    D --> E{Performance<br/>Analysis}
    
    E -->|Weak (1-2)| F[Easier Questions<br/>• Basic scenarios<br/>• Fundamental concepts<br/>• Confidence building]
    E -->|Average (3-4)| G[Same Level Questions<br/>• Maintain difficulty<br/>• Balanced challenge<br/>• Skill reinforcement]
    E -->|Strong (4-5)| H[Harder Questions<br/>• Complex scenarios<br/>• Advanced concepts<br/>• Stretch challenges]
    
    F --> I[Template Selection<br/>• Match question type<br/>• Match difficulty level<br/>• Avoid repetition<br/>• Consider context]
    G --> I
    H --> I
    
    I --> J[Personalization<br/>• Insert CV details<br/>• Add specific context<br/>• Customize examples<br/>• Reference experience]
    
    J --> K[AI Prompt Construction<br/>• System prompt<br/>• User context<br/>• Question requirements<br/>• Format specifications]
    
    K --> L[AI Service Selection<br/>• Primary: Groq API<br/>• Fallback: Ollama<br/>• Error handling<br/>• Retry logic]
    
    L --> M[AI Model Processing<br/>• Send prompt<br/>• Get response<br/>• Parse result<br/>• Handle errors]
    
    M --> N[Quality Assurance<br/>• Check format<br/>• Validate content<br/>• Ensure uniqueness<br/>• Verify relevance]
    
    N --> O{Quality Check<br/>Passed?}
    
    O -->|No| P[Regenerate Question<br/>• Try different template<br/>• Adjust parameters<br/>• Retry AI call<br/>• Log attempt]
    
    O -->|Yes| Q[Post-Processing<br/>• Format cleaning<br/>• Grammar check<br/>• Clarity enhancement<br/>• Professional tone]
    
    P --> K
    
    Q --> R[Store Question<br/>• Save to database<br/>• Update context<br/>• Track usage<br/>• Log metadata]
    
    R --> S[Return to User<br/>• Display question<br/>• Wait for response<br/>• Track timing<br/>• Monitor engagement]
    
    S --> T[Update Context<br/>• Add to question history<br/>• Prepare for next round<br/>• Update user state<br/>• Calculate progress]
    
    T --> U{Session<br/>Complete?}
    
    U -->|No| B
    U -->|Yes| V[End Session<br/>• Generate summary<br/>• Save session data<br/>• Provide feedback<br/>• Update user stats]
    
    %% Styling
    classDef startEnd fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
    classDef process fill:#e1f5fe,stroke:#2196f3,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    classDef ai fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px
    classDef quality fill:#fff8e1,stroke:#ffc107,stroke-width:2px
    classDef storage fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
    classDef difficulty fill:#ffebee,stroke:#f44336,stroke-width:2px
    
    class A,V startEnd
    class B,C,D,I,J,S,T process
    class E,O,U decision
    class K,L,M ai
    class N,P,Q quality
    class R storage
    class F,G,H difficulty
```

## Algorithm Description

### 1. Session Initialization
- Load user profile and CV data
- Initialize conversation context
- Set session parameters and goals

### 2. Context Analysis
- Analyze CV data for relevant information
- Assess user experience level
- Review previous responses and performance
- Consider question history to avoid repetition

### 3. Question Type Selection
- **Behavioral**: Past behavior and experiences
- **Technical**: Skills and knowledge assessment
- **Situational**: Hypothetical scenario responses
- **Experience-based**: Specific work experiences
- **Skills-focused**: Competency demonstrations

### 4. Adaptive Difficulty Logic
- **Performance Assessment**: Analyze previous response quality
- **Dynamic Adjustment**: Modify difficulty based on performance
- **Progressive Challenge**: Gradually increase complexity
- **Confidence Building**: Support struggling users

### 5. Personalization Process
- Insert specific CV details (companies, projects, technologies)
- Add contextual information from user background
- Customize examples to match experience level
- Reference specific achievements and skills

### 6. AI Integration
- **Primary Service**: Groq API with Gemini 2.0 Flash
- **Fallback Service**: Local Ollama with llama3
- **Error Handling**: Robust retry mechanisms
- **Quality Control**: Response validation and filtering

### 7. Question Validation
- Format checking and consistency
- Content relevance verification
- Uniqueness assurance (no duplicates)
- Appropriateness for user level

### 8. Continuous Learning
- Track question effectiveness
- Monitor user engagement
- Adjust algorithms based on feedback
- Improve personalization over time

## Key Features

### Adaptive Intelligence
- Questions become more challenging as user improves
- Difficulty adjusts based on real-time performance
- Personalized learning path for each user

### Quality Assurance
- Multi-layer validation process
- Automatic regeneration for poor quality questions
- Professional tone and clarity standards

### Context Awareness
- Deep integration with CV data
- Conversation history consideration
- Progressive question building

### Reliability
- Dual AI provider strategy
- Comprehensive error handling
- Graceful degradation capabilities
