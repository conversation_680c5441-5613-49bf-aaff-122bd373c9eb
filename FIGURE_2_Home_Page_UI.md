# Figure 2: User Interface - Home Page

## Description
The home page serves as the main entry point for the AI Job Interview Coach application, providing users with an overview of features and clear navigation to start their interview preparation journey.

## Key UI Elements

### Header Section
```
┌─────────────────────────────────────────────────────────────────────────────┐
│                           AI JOB INTERVIEW COACH                           │
│                                                                             │
│  [Logo]  Home    Features    About    Login    Sign Up    [Theme Toggle]   │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Hero Section
```
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│                    🎯 Master Your Next Interview                           │
│                                                                             │
│              AI-powered interview practice with personalized               │
│                    feedback based on your CV                               │
│                                                                             │
│                    [Get Started] [Learn More]                              │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Features Overview
```
┌─────────────────────────────────────────────────────────────────────────────┐
│                              Key Features                                  │
│                                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐            │
│  │  📄 CV Analysis │  │ 🤖 AI Questions │  │ ⭐ STAR Feedback│            │
│  │                 │  │                 │  │                 │            │
│  │ Upload your CV  │  │ Personalized    │  │ Structured      │            │
│  │ for personalized│  │ questions based │  │ feedback using  │            │
│  │ interview       │  │ on your         │  │ industry-       │            │
│  │ questions       │  │ background      │  │ standard STAR   │            │
│  │                 │  │                 │  │ methodology     │            │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘            │
│                                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐            │
│  │ 📈 Progress     │  │ 💬 Real-time    │  │ 🎯 Adaptive     │            │
│  │ Tracking        │  │ Simulation      │  │ Difficulty      │            │
│  │                 │  │                 │  │                 │            │
│  │ Monitor your    │  │ Interactive     │  │ Questions adjust│            │
│  │ improvement     │  │ chat interface  │  │ based on your   │            │
│  │ over multiple   │  │ that feels like │  │ performance and │            │
│  │ sessions        │  │ a real interview│  │ experience level│            │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘            │
└─────────────────────────────────────────────────────────────────────────────┘
```

### How It Works Section
```
┌─────────────────────────────────────────────────────────────────────────────┐
│                              How It Works                                  │
│                                                                             │
│    1️⃣ Upload CV    →    2️⃣ AI Analysis    →    3️⃣ Practice    →    4️⃣ Improve │
│                                                                             │
│  Upload your CV      System analyzes      Start interview     Get detailed │
│  in DOCX format      your background      simulation with     STAR-based   │
│                      and experience       personalized       feedback and  │
│                                          questions          suggestions    │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Call to Action
```
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│                        Ready to ace your interview?                        │
│                                                                             │
│                           [Start Practicing Now]                           │
│                                                                             │
│                    Already have an account? [Sign In]                      │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Footer
```
┌─────────────────────────────────────────────────────────────────────────────┐
│  AI Job Interview Coach © 2025  |  Privacy Policy  |  Terms of Service     │
│                                                                             │
│  Built with React, TypeScript, and advanced AI technology                  │
└─────────────────────────────────────────────────────────────────────────────┘
```

## Design Specifications

### Color Scheme
- **Primary**: Blue (#1976d2) for main actions and highlights
- **Secondary**: Green (#4caf50) for success states and positive feedback
- **Background**: White (#ffffff) in light mode, Dark gray (#121212) in dark mode
- **Text**: Dark gray (#333333) in light mode, Light gray (#ffffff) in dark mode

### Typography
- **Headings**: Roboto, 24-32px, Bold
- **Body Text**: Roboto, 16px, Regular
- **Buttons**: Roboto, 14px, Medium

### Interactive Elements
- **Buttons**: Rounded corners (8px), hover effects with color transitions
- **Cards**: Subtle shadows, hover elevation effects
- **Navigation**: Smooth transitions between sections

### Responsive Design
- **Desktop**: Full-width layout with side-by-side feature cards
- **Tablet**: Stacked layout with 2-column feature grid
- **Mobile**: Single-column layout with full-width elements

## User Flow
1. User lands on home page
2. Reads feature overview and value proposition
3. Clicks "Get Started" or "Start Practicing Now"
4. Redirected to registration/login if not authenticated
5. After authentication, directed to user profile/dashboard page

## Accessibility Features
- High contrast ratios for text readability
- Keyboard navigation support
- Screen reader compatible with proper ARIA labels
- Focus indicators for interactive elements
- Alternative text for icons and images
